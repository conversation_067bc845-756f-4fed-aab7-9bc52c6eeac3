package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.ConversaAssistente;
import br.com.estudoorganizado.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConversaAssistenteRepository extends JpaRepository<ConversaAssistente, Long> {
    
    /**
     * <PERSON>ca todas as conversas de um usuário ordenadas pela data da última mensagem
     */
    @Query("SELECT c FROM ConversaAssistente c WHERE c.user = :user ORDER BY c.dataUltimaMensagem DESC")
    Page<ConversaAssistente> findByUserOrderByDataUltimaMensagemDesc(@Param("user") User user, Pageable pageable);
    
    /**
     * <PERSON><PERSON> todas as conversas de um usuário ordenadas pela data da última mensagem
     */
    List<ConversaAssistente> findByUserOrderByDataUltimaMensagemDesc(User user);
    
    /**
     * Busca uma conversa específica de um usuário
     */
    Optional<ConversaAssistente> findByIdAndUser(Long id, User user);
    
    /**
     * Conta o número de conversas de um usuário
     */
    long countByUser(User user);
    
    /**
     * Busca conversas por título contendo texto (case insensitive)
     */
    @Query("SELECT c FROM ConversaAssistente c WHERE c.user = :user AND LOWER(c.titulo) LIKE LOWER(CONCAT('%', :titulo, '%')) ORDER BY c.dataUltimaMensagem DESC")
    List<ConversaAssistente> findByUserAndTituloContainingIgnoreCase(@Param("user") User user, @Param("titulo") String titulo);
}
