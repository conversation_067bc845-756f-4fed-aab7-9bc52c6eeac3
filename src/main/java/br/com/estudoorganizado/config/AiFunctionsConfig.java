package br.com.estudoorganizado.config;

import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.PlanejamentoDTO;
import br.com.estudoorganizado.dto.PlanejamentoIARequestDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.DisciplinaService;
import br.com.estudoorganizado.service.PlanejamentoInteligentService;
import br.com.estudoorganizado.service.PlanejamentoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Description;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.function.Function;

@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class AiFunctionsConfig {

    private final DisciplinaService disciplinaService;
    private final PlanejamentoService planejamentoService;
    private final PlanejamentoInteligentService planejamentoInteligentService;

    @Bean
    @Description("Cadastra uma nova disciplina no sistema. Requer o nome da disciplina como parâmetro.")
    public Function<CadastrarDisciplinaRequest, String> cadastrarDisciplina() {
        return request -> {
            try {
                User user = getCurrentUser();
                
                DisciplinaDTO disciplinaDTO = new DisciplinaDTO();
                disciplinaDTO.setNome(request.nome());
                
                DisciplinaDTO disciplinaSalva = disciplinaService.save(disciplinaDTO, user);
                
                log.info("Disciplina '{}' cadastrada com sucesso via IA para usuário: {}", 
                    disciplinaSalva.getNome(), user.getEmail());
                
                return String.format("Disciplina '%s' foi cadastrada com sucesso com o ID %d.", 
                    disciplinaSalva.getNome(), disciplinaSalva.getId());
                    
            } catch (Exception e) {
                log.error("Erro ao cadastrar disciplina via IA: {}", e.getMessage());
                return "Erro ao cadastrar a disciplina: " + e.getMessage();
            }
        };
    }

    @Bean
    @Description("Cria ou atualiza um plano de estudo em ciclo. Requer os dados do planejamento incluindo nome, horas semanais, duração máxima por sessão e ciclo de estudo com disciplinas.")
    public Function<SalvarPlanejamentoRequest, String> salvarOuAtualizarPlanejamento() {
        return request -> {
            try {
                User user = getCurrentUser();
                
                // Converter request para PlanejamentoDTO
                PlanejamentoDTO planejamentoDTO = new PlanejamentoDTO();
                planejamentoDTO.setNome(request.nome());
                planejamentoDTO.setHorasDisponiveisPorSemana(request.horasSemanais());
                planejamentoDTO.setMinutosDuracaoMaximaPorSessao(request.duracaoMaximaSessao());
                
                // Aqui seria necessário converter o ciclo de estudo também
                // Por simplicidade, vamos usar o método de sugestão automática
                PlanejamentoDTO planejamentoSalvo = planejamentoService.sugerirPlanejamentoCicloEstudo(planejamentoDTO);
                
                log.info("Planejamento '{}' salvo com sucesso via IA para usuário: {}", 
                    planejamentoSalvo.getNome(), user.getEmail());
                
                return String.format("Plano de estudos '%s' foi criado/atualizado com sucesso.", 
                    planejamentoSalvo.getNome());
                    
            } catch (Exception e) {
                log.error("Erro ao salvar planejamento via IA: {}", e.getMessage());
                return "Erro ao criar/atualizar o plano de estudos: " + e.getMessage();
            }
        };
    }

    @Bean
    @Description("Gera um planejamento de estudos em ciclo utilizando IA e persiste no banco. Requer objetivo de estudo, horas semanais disponíveis, duração máxima por sessão e lista de disciplinas com seus respectivos níveis e pesos.")
    public Function<CriarPlanejamentoIARequest, String> criarPlanejamentoComIA() {
        return request -> {
            try {
                User user = getCurrentUser();
                
                // Converter request para PlanejamentoIARequestDTO
                PlanejamentoIARequestDTO planejamentoIARequest = new PlanejamentoIARequestDTO();
                planejamentoIARequest.setObjetivoEstudo(request.objetivoEstudo());
                planejamentoIARequest.setHorasDisponiveisPorSemana(request.horasSemanais());
                planejamentoIARequest.setMinutosDuracaoMaximaPorSessao(request.duracaoMaximaSessao());
                planejamentoIARequest.setDisciplinas(request.disciplinas());
                
                PlanejamentoDTO planejamentoSalvo = planejamentoInteligentService.criarPlanejamentoComIA(planejamentoIARequest, user);
                
                log.info("Planejamento com IA '{}' criado com sucesso para usuário: {}", 
                    planejamentoSalvo.getNome(), user.getEmail());
                
                return String.format("Planejamento de estudos '%s' foi gerado com IA e salvo com sucesso. " +
                    "O plano inclui %d horas semanais distribuídas entre as disciplinas especificadas.", 
                    planejamentoSalvo.getNome(), planejamentoSalvo.getHorasDisponiveisPorSemana());
                    
            } catch (Exception e) {
                log.error("Erro ao criar planejamento com IA: {}", e.getMessage());
                return "Erro ao gerar planejamento com IA: " + e.getMessage();
            }
        };
    }

    private User getCurrentUser() {
        return (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    // Records para os parâmetros das funções
    public record CadastrarDisciplinaRequest(String nome) {}
    
    public record SalvarPlanejamentoRequest(
        String nome, 
        Integer horasSemanais, 
        Integer duracaoMaximaSessao
    ) {}
    
    public record CriarPlanejamentoIARequest(
        String objetivoEstudo,
        Integer horasSemanais,
        Integer duracaoMaximaSessao,
        java.util.List<br.com.estudoorganizado.dto.DisciplinaIADTO> disciplinas
    ) {}
}
