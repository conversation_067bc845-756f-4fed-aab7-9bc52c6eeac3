package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.ChatRequestDTO;
import br.com.estudoorganizado.dto.ChatResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/v1/assistant")
@Tag(name = "Assistant", description = "Assistente de IA conversacional")
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class AssistantController {

    private final ChatClient chatClient;

    public AssistantController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder.build();
    }

    @PostMapping("/chat")
    @Operation(summary = "Chat com assistente de IA", 
               description = "Envia uma mensagem para o assistente de IA que pode executar operações no sistema através de function calling")
    public ResponseEntity<ChatResponseDTO> chat(@Valid @RequestBody ChatRequestDTO request) {
        try {
            log.info("Recebida mensagem para o assistente: {}", request.getMensagem());

            // Usar ChatClient com function calling habilitado
            String resposta = chatClient.prompt()
                .user(request.getMensagem())
                .call()
                .content();

            log.info("Resposta gerada pelo assistente");
            
            return ResponseEntity.ok(new ChatResponseDTO(resposta));
            
        } catch (Exception e) {
            log.error("Erro ao processar mensagem do assistente: {}", e.getMessage(), e);
            return ResponseEntity.ok(new ChatResponseDTO(
                "Desculpe, ocorreu um erro ao processar sua solicitação. Tente novamente."
            ));
        }
    }
}
