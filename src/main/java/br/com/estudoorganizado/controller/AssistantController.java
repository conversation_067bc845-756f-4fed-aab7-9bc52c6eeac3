package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.model.ConversaAssistente;
import br.com.estudoorganizado.model.MensagemConversa;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.ConversaAssistenteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v1/assistant")
@Tag(name = "Assistant", description = "Assistente de IA conversacional")
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class AssistantController {

    private final ChatClient chatClient;
    private final ConversaAssistenteService conversaService;

    public AssistantController(ChatClient.Builder chatClientBuilder, ConversaAssistenteService conversaService) {
        this.chatClient = chatClientBuilder.build();
        this.conversaService = conversaService;
    }

    @PostMapping("/chat")
    @Operation(summary = "Chat com assistente de IA com histórico",
               description = "Envia uma mensagem para o assistente de IA mantendo o contexto da conversa")
    public ResponseEntity<ChatComHistoricoResponseDTO> chat(@Valid @RequestBody ChatComHistoricoRequestDTO request,
                                                            @AuthenticationPrincipal User user) {
        try {
            log.info("Recebida mensagem para o assistente do usuário: {}", user.getEmail());

            // Buscar ou criar conversa
            ConversaAssistente conversa = obterOuCriarConversa(request, user);

            // Salvar mensagem do usuário
            conversaService.adicionarMensagem(conversa, request.getMensagem(), MensagemConversa.TipoMensagem.USER);

            // Buscar histórico para contexto
            List<MensagemConversaDTO> historicoRecente = conversaService.buscarUltimasMensagens(conversa, 10);

            // Construir contexto da conversa
            List<Message> mensagensContexto = construirContextoConversa(historicoRecente);

            // Gerar resposta com contexto
            String resposta = chatClient.prompt()
                .messages(mensagensContexto)
                .call()
                .content();

            // Salvar resposta do assistente
            conversaService.adicionarMensagem(conversa, resposta, MensagemConversa.TipoMensagem.ASSISTANT);

            log.info("Resposta gerada pelo assistente para conversa: {}", conversa.getId());

            return ResponseEntity.ok(ChatComHistoricoResponseDTO.builder()
                .resposta(resposta)
                .conversaId(conversa.getId())
                .tituloConversa(conversa.getTitulo())
                .novaConversa(request.getConversaId() == null)
                .build());

        } catch (Exception e) {
            log.error("Erro ao processar mensagem do assistente: {}", e.getMessage(), e);
            return ResponseEntity.ok(ChatComHistoricoResponseDTO.builder()
                .resposta("Desculpe, ocorreu um erro ao processar sua solicitação. Tente novamente.")
                .build());
        }
    }

    @PostMapping("/chat-simples")
    @Operation(summary = "Chat simples sem histórico",
               description = "Envia uma mensagem para o assistente de IA sem manter contexto (compatibilidade)")
    public ResponseEntity<ChatResponseDTO> chatSimples(@Valid @RequestBody ChatRequestDTO request) {
        try {
            log.info("Recebida mensagem para chat simples: {}", request.getMensagem());

            String resposta = chatClient.prompt()
                .user(request.getMensagem())
                .call()
                .content();

            log.info("Resposta gerada pelo assistente (chat simples)");

            return ResponseEntity.ok(new ChatResponseDTO(resposta));

        } catch (Exception e) {
            log.error("Erro ao processar mensagem do assistente: {}", e.getMessage(), e);
            return ResponseEntity.ok(new ChatResponseDTO(
                "Desculpe, ocorreu um erro ao processar sua solicitação. Tente novamente."
            ));
        }
    }

    private ConversaAssistente obterOuCriarConversa(ChatComHistoricoRequestDTO request, User user) {
        if (request.getConversaId() != null) {
            return conversaService.buscarConversaPorId(request.getConversaId(), user)
                .orElseThrow(() -> new RuntimeException("Conversa não encontrada"));
        } else {
            String titulo = request.getTituloConversa() != null ?
                          request.getTituloConversa() :
                          conversaService.gerarTituloAutomatico(request.getMensagem());
            return conversaService.criarNovaConversa(user, titulo);
        }
    }

    private List<Message> construirContextoConversa(List<MensagemConversaDTO> historico) {
        List<Message> mensagens = new ArrayList<>();

        for (MensagemConversaDTO msg : historico) {
            if (msg.getTipo() == MensagemConversa.TipoMensagem.USER) {
                mensagens.add(new UserMessage(msg.getConteudo()));
            } else {
                mensagens.add(new AssistantMessage(msg.getConteudo()));
            }
        }

        return mensagens;
    }

    @GetMapping("/conversas")
    @Operation(summary = "Listar conversas do usuário",
               description = "Retorna todas as conversas do usuário ordenadas pela data da última mensagem")
    public ResponseEntity<List<ConversaAssistenteDTO>> listarConversas(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<ConversaAssistenteDTO> conversas = conversaService.listarConversasDoUsuario(user);
            return ResponseEntity.ok(conversas);
        } catch (Exception e) {
            log.error("Erro ao listar conversas: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/conversas/{conversaId}")
    @Operation(summary = "Obter histórico de uma conversa",
               description = "Retorna o histórico completo de mensagens de uma conversa específica")
    public ResponseEntity<ConversaAssistenteDTO> obterHistoricoConversa(@PathVariable Long conversaId,
                                                                       Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            ConversaAssistenteDTO conversa = conversaService.buscarHistoricoCompleto(conversaId, user);

            if (conversa == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(conversa);
        } catch (Exception e) {
            log.error("Erro ao obter histórico da conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/conversas")
    @Operation(summary = "Criar nova conversa",
               description = "Cria uma nova conversa vazia para o usuário")
    public ResponseEntity<ConversaAssistenteDTO> criarNovaConversa(@RequestBody(required = false) String titulo,
                                                                  Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String tituloConversa = titulo != null && !titulo.trim().isEmpty() ? titulo.trim() : "Nova Conversa";

            ConversaAssistente conversa = conversaService.criarNovaConversa(user, tituloConversa);

            ConversaAssistenteDTO conversaDTO = ConversaAssistenteDTO.builder()
                .id(conversa.getId())
                .titulo(conversa.getTitulo())
                .dataCriacao(conversa.getDataCriacao())
                .dataUltimaMensagem(conversa.getDataUltimaMensagem())
                .totalMensagens(0)
                .build();

            return ResponseEntity.ok(conversaDTO);
        } catch (Exception e) {
            log.error("Erro ao criar nova conversa: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/conversas/{conversaId}/titulo")
    @Operation(summary = "Atualizar título da conversa",
               description = "Atualiza o título de uma conversa específica")
    public ResponseEntity<Void> atualizarTituloConversa(@PathVariable Long conversaId,
                                                        @RequestBody String novoTitulo,
                                                        Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            ConversaAssistente conversa = conversaService.buscarConversaPorId(conversaId, user)
                .orElse(null);

            if (conversa == null) {
                return ResponseEntity.notFound().build();
            }

            conversaService.atualizarTitulo(conversa, novoTitulo.trim());
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Erro ao atualizar título da conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/conversas/{conversaId}")
    @Operation(summary = "Remover conversa",
               description = "Remove uma conversa e todo seu histórico de mensagens")
    public ResponseEntity<Void> removerConversa(@PathVariable Long conversaId,
                                               Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            conversaService.removerConversa(conversaId, user);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Erro ao remover conversa {}: {}", conversaId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
